<?php
declare(strict_types=1);

namespace App\Application\Session;

use App\Models\Auth\Login;


class SessionManager extends Login
{

    public function __construct()
    {
        parent::__construct();
        $this->start();
    }

    /**
     * Start session
     *
     * @return void
     */
    private function start(): void
    {
        $status = session_status();
        switch ($status){
            case PHP_SESSION_DISABLED:
                throw new \RuntimeException('Sessions are disabled');
                break;
            case PHP_SESSION_NONE:
                // Configure session before starting
                session_name("_integration");

                // Set session cookie parameters
                session_set_cookie_params([
                    'lifetime' => 3600, // 1 hour
                    'path' => '/',
                    'domain' => '',
                    'secure' => false, // Set to true in production with HTTPS
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]);

                session_start();

                // Initialize session data if not logged in
                if (!$this->isLoggedIn()) {
                    $this->initializeEmptySession();
                }
                break;
            case PHP_SESSION_ACTIVE:
                // Load existing session data
                $this->loadSessionData();
                break;
        }
    }

    /**
     * Check if a user is logged in
     *
     * @return bool
     */
    public function isLoggedIn(): bool
    {
        if (!isset($_SESSION['login_data']['is_successful'])) {
            return false;
        }else{
            return (bool)$_SESSION['login_data']['is_successful'];
        }
    }

    public function logout(): void
    {
        $this->destroySession();
    }

    public function ValidatePassword(string $passwordHash, string $password): bool
    {
        return password_verify($this->paswordHash($password), $passwordHash);
    }

    public function setLoginData($session_data = [], bool $remember_me = false, bool $is_successful = false): void
    {
        // Fix logic error: use session data if available, otherwise use parameter
        $remember_me = $session_data['remember_me'] ?? $remember_me;
        $is_successful = $session_data['is_successful'] ?? $is_successful;

        $this->setUserId($session_data['user_id'] ?? 0);
        $this->setUsername($session_data['username'] ?? '');
        $this->setEmail($session_data['email'] ?? '');
        $this->setFirstName($session_data['first_name'] ?? '');
        $this->setLastName($session_data['last_name'] ?? '');
        $this->setLoginTime($session_data['login_time'] ?? date('Y-m-d H:i:s'));
        $this->setIpAddress($session_data['ip_address'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $this->setIsSuccessful($is_successful);
        $this->setRememberMe($remember_me);

        // Update session with current data
        $_SESSION['login_data'] = $this->toArray();
    }

    private function setSessionData(): void
    {
        $_SESSION['login_data'] = $this->toArray();
    }

    /**
     * Initialize empty session for new users
     *
     * @return void
     */
    private function initializeEmptySession(): void
    {
        // Set default values for new session
        $this->setUserId(0);
        $this->setUsername('');
        $this->setEmail('');
        $this->setFirstName('');
        $this->setLastName('');
        $this->setLoginTime(date('Y-m-d H:i:s'));
        $this->setIpAddress($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $this->setIsSuccessful(false);
        $this->setRememberMe(false);

        // Save to session
        $_SESSION['login_data'] = $this->toArray();
    }

    /**
     * Load existing session data
     *
     * @return void
     */
    private function loadSessionData(): void
    {
        if (isset($_SESSION['login_data']) && is_array($_SESSION['login_data'])) {
            $this->setLoginData($_SESSION['login_data']);
        } else {
            // If session data is corrupted or missing, initialize empty session
            $this->initializeEmptySession();
        }
    }

    /**
     * Regenerate session ID for security
     *
     * @return void
     */
    public function regenerateId(): void
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_regenerate_id(true);
        }
    }

    /**
     * Destroy session and clear all data
     *
     * @return void
     */
    public function destroySession(): void
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Clear session data
            $_SESSION = [];

            // Delete session cookie
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }

            // Destroy session
            session_destroy();
        }
    }
}
