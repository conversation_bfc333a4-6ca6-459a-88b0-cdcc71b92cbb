<?php
declare(strict_types=1);

namespace App\Application\Session;

use App\Models\Auth\Login;


class SessionManager extends Login
{

    public function __construct()
    {
        parent::__construct();
        $this->start();
    }

    /**
     * Start session
     *
     * @return void
     */
    private function start(): void
    {
        $status = session_status();
        switch ($status){
            case PHP_SESSION_DISABLED:
                $return = false;
                throw new \RuntimeException('Sessions are disabled');
                break;
            case PHP_SESSION_NONE:
                session_name("_integration");
                setcookie('_integration', session_id(), [
                    'expires' => time() + 3600,
                    'path' => '/',
                    'domain' => '',
                    'secure' => false,
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]);
                session_start();

                if(!$this->isLoggedIn()) {
                    $this->setLoginData($_SESSION['login_data'] ?? [], false, false);
                    $this->setSessionData();
                }
              break;
            case PHP_SESSION_ACTIVE:
                $this->setLoginData($_SESSION['login_data'] ?? []);
                break;
        }
    }

    /**
     * Check if a user is logged in
     *
     * @return bool
     */
    public function isLoggedIn(): bool
    {
        if (!isset($_SESSION['login_data']['is_successful'])) {
            return false;
        }else{
            return (bool)$_SESSION['login_data']['is_successful'];
        }
    }

    public function logout(): void
    {
        session_destroy();
    }

    public function ValidatePassword(string $passwordHash, string $password): bool
    {
        return password_verify($this->paswordHash($password), $passwordHash);
    }

    public function setLoginData($session_data = [], bool $remember_me = false, bool $is_successful = false): void
    {
        $remember_me = !$remember_me ?? $session_data['remember_me'];
        $is_successful = (isset($session_data['is_successful'])) ? $session_data['is_successful'] : $is_successful;
        $this->setUserId($session_data['user_id']?? 0);
        $this->setUsername($session_data['username'] ?? '');
        $this->setEmail($session_data['email'] ?? '');
        $this->setFirstName($session_data['first_name'] ?? '');
        $this->setLastName($session_data['last_name'] ?? '');
        $this->setLoginTime(date('Y-m-d H:i:s'));
        $this->setIpAddress($_SERVER['REMOTE_ADDR']);
        $this->setIsSuccessful($is_successful);
        $this->setRememberMe($remember_me);
        $_SESSION['login_data'] = $this->toArray();
    }

    private function setSessionData(): void
    {
        $_SESSION['login_data'] = $this->toArray();
    }


}
