<?php
declare(strict_types=1);

namespace App\Controllers\Pages;

use App\Helper\ControllersExceptions;
use App\Models\Pages\Users\UserFactory;
use App\Application\Session\SessionManager;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Slim\Views\Twig;

class LoginPage
{
    protected ControllersExceptions $controller;
    protected UserFactory $userFactory;
    private LoggerInterface $logger;
    private SessionManager $sessionManager;

    public function __construct(
        LoggerInterface $logger,
        ControllersExceptions $controller,
        UserFactory $userFactory,
        ?SessionManager $sessionManager = null
    ) {
        $this->userFactory = $userFactory;
        $this->controller = $controller;
        $this->logger = $logger;
        $this->sessionManager = $sessionManager ?? new SessionManager();
    }

    public function IndexPage(Request $request, Response $response, array $args)
    {
        $template = Twig::FromRequest($request);

        $args = array_merge($args, [
            'title' => 'Login',
            'message' => 'Please login'
        ]);
        return $template->render($response, 'login.html', $args);
    }

    public function Authenticate(Request $request, Response $response, array $args)
    {
        $controller = [];
        $paramsExpected = ['username', 'password','remember_me'];
        $message = 'Invalid request: missing parameters';

        try {
            // Validate request parameters
            $controller = $this->controller->HelperControllersExceptions($request->getParsedBody(), $paramsExpected);
            if (isset($controller['exception'])) {
                throw new \RuntimeException($message);
            }

            // get Information user
            $userData = $this->userFactory->getUserEmailOrUsername($controller['username']);
            if (isset($userData['exception']) || empty($userData) || isset($userData['error'])) {
                throw new \RuntimeException('Authentication failed');
            }

            // Verify password hash
            if (!$this->sessionManager->ValidatePassword($userData['password_hash'], $controller['password']) && $userData['is_active'] === 0) {
                $this->sessionManager->setLoginData([], false, true);
                throw new \RuntimeException('Authentication failed');
            }else{
                // Regenerate session ID for security
                $this->sessionManager->regenerateId();

                $this->sessionManager->setLoginData($userData, $controller['remember_me'], true);
                // Return success response with redirect
                $responseData = [
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect' => '/'
                ];

                $response->getBody()->write(json_encode($responseData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));
                return $response
                    ->withHeader('Content-Type', 'application/json')
                    ->withStatus(200);
            }

        } catch (\Throwable $th) {
            $return = [
                'exception' => 07,
                'file' => $th->getFile(),
                'line' => $th->getLine(),
                'code' => $th->getCode(),
                'status_code' => 401,
                'message' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ];
            $this->logger->error("Error : ", $return);

            $errorResponse = [
                'success' => false,
                'error' => true,
                'message' => 'Authentication failed. Please check your credentials and try again.'
            ];

            $response->getBody()->write(json_encode($errorResponse, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));
            return $response
                ->withHeader('Content-Type', 'application/json')
                ->withStatus(401);
        }
    }

    /**
     * Logout user
     *
     * @param Request $request
     * @param Response $response
     * @param array $args
     * @return Response
     */
    public function logout(Request $request, Response $response, array $args): Response
    {
        try {

            $this->sessionManager->logout();

            return $response
                ->withHeader('Location', '/')
                ->withStatus(302);

        } catch (\Throwable $th) {
            $return = [
                'exception' => 01,
                'file' => $th->getFile(),
                'line' => $th->getLine(),
                'code' => $th->getCode(),
                'status_code' => 302,
                'message' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ];
            $this->logger->error("Error : ", $return);

            return $response
                ->withHeader('Location', '/')
                ->withStatus(302);
        }
    }
}
